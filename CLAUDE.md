# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Mobile Use is an AI Agent solution that automates mobile tasks through natural language instructions. It integrates:
- **Volcano Engine Cloud Phone** for mobile device simulation
- **Doubao Visual Large Model** for AI reasoning
- **MCP (Model Context Protocol)** for tool integration

## Architecture

The project consists of three main components:

### 1. Mobile Agent (Python) - `/mobile_agent/`
- **Framework**: FastAPI + LangGraph + MCP
- **Purpose**: Core AI agent logic, vision processing, task orchestration
- **Key Files**:
  - `main.py` - Application entry point
  - `mobile_agent/agent/mobile_use_agent.py` - Main agent class
  - `mobile_agent/agent/graph/` - LangGraph workflow definitions
  - `mobile_agent/agent/tools/` - Tool management and MCP integration

### 2. MCP Server (Go) - `/mobile_use_mcp/`
- **Purpose**: Cloud phone interaction layer with standard MCP protocol
- **Key Files**:
  - `cmd/mobile_use_mcp/main.go` - MCP server entry point
  - `internal/mobile_use/tool/` - Mobile automation tools
- **Supported Tools**: screenshot, tap, swipe, text_input, app management

### 3. Web Frontend (Next.js) - `/web/`
- **Framework**: Next.js 15 + React 19 + TypeScript + Tailwind CSS
- **Key Features**: Real-time chat interface, mobile screen visualization, SSE streaming
- **Key Files**:
  - `src/app/` - Next.js app router structure
  - `src/components/chat/` - Chat interface components
  - `src/components/phone/` - Mobile screen components

## Development Commands

### Setup & Installation
```bash
# Install all dependencies
sh setup.sh

# Individual component setup
cd mobile_agent && uv sync
cd web && npm install
cd mobile_use_mcp && go mod tidy
```

### Development Servers
```bash
# Start MCP server (required first)
cd mobile_use_mcp
go run cmd/mobile_use_mcp/main.go -t streamable-http -p 8888

# Start mobile agent
cd mobile_agent
uv run main.py

# Start web frontend
cd web
npm run dev
```

### Building
```bash
# Build all components
sh build.sh

# Individual builds
cd mobile_agent && ./build-vefaas.sh
cd web && npm run build
cd mobile_use_mcp && ./build.sh
```

### Testing & Linting
```bash
# Web frontend
cd web
npm run lint

# Python (mobile_agent)
cd mobile_agent
uv run pytest  # if tests exist
```

## Configuration

### Environment Variables
All components require specific environment variables:

**Mobile Agent** (`.env`):
- `MOBILE_USE_MCP_URL` - MCP service URL
- `ARK_API_KEY` / `ARK_MODEL_ID` - Doubao model credentials
- `TOS_BUCKET` / `TOS_REGION` / `TOS_ENDPOINT` - Object storage
- `ACEP_AK` / `ACEP_SK` / `ACEP_ACCOUNT_ID` - Cloud phone credentials

**Web Frontend** (`.env`):
- `CLOUD_AGENT_BASE_URL` - Agent service URL

### Configuration Files
- `mobile_agent/config.toml` - Agent and LLM configuration
- `web/next.config.ts` - Next.js configuration (standalone output)
- `mobile_use_mcp/go.mod` - Go dependencies

## Key Design Patterns

### Mobile Agent Architecture
- **LangGraph State Machine**: Manages complex agent workflows
- **MCP Integration**: Standard protocol for tool communication
- **Async Processing**: Uses FastAPI for non-blocking operations
- **Memory Management**: Context and message persistence
- **Cost Tracking**: Built-in cost calculation for model usage

### Web Frontend Architecture
- **SSE Streaming**: Real-time communication with agent
- **State Management**: Jotai for global state
- **Component Library**: Radix UI + shadcn/ui components
- **Responsive Design**: Mobile-first approach with resizable panels

### MCP Server Architecture
- **Multi-Transport**: Supports stdio, SSE, and streamable-http
- **Tool Registry**: Standardized tool interface
- **Cloud Phone Integration**: Direct API integration with Volcano Engine

## API Endpoints

### Mobile Agent REST API
- `POST /api/agent/stream` - Start AI agent task with streaming
- `POST /api/agent/cancel` - Cancel running agent task
- `POST /api/session/create` - Create new session
- `POST /api/session/reset` - Reset session state

### Available Mobile Tools
- `take_screenshot` - Capture device screen
- `tap` - Tap at coordinates
- `swipe` - Swipe gestures
- `text_input` - Input text
- `home` / `back` / `menu` - Navigation
- `launch_app` / `close_app` / `list_apps` - App management
- `autoinstall_app` - Auto-install applications

## Development Notes

### Port Configuration
- Web Frontend: 8080
- Mobile Agent: 8000
- MCP Server: 8888

### Dependency Management
- Python: Use `uv` for package management
- Node.js: Use `npm` (v20+ required)
- Go: Standard `go mod` (v1.23+ required)

### Build Outputs
- Mobile Agent: ZIP packages in `mobile_agent/`
- Web: Standalone Next.js in `.next/standalone/`
- MCP Server: Linux binary in `output/mobile_use_mcp`

## Integration Points

### Agent ↔ MCP Communication
- Uses MCP protocol over streamable-http transport
- Headers include authentication and device context
- Async tool execution with streaming responses

### Web ↔ Agent Communication
- FastAPI SSE endpoints for real-time updates
- JSON message format with streaming support
- Session management for multi-turn conversations

### Authentication Flow
- Token-based authentication for web access
- Volcano Engine credentials for cloud phone access
- TOS integration for screenshot storage