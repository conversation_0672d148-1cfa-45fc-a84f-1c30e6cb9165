<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_dii_6818_37772)">
<path d="M12.6666 10.0042C12.6666 7.42681 14.7559 5.3374 17.3333 5.3374H45.7641L67.3315 26.8072L67.333 69.9999C67.3331 72.5773 65.2437 74.6667 62.6663 74.6667H17.3348C14.7575 74.6667 12.6682 72.5774 12.6681 70.0002L12.6666 10.0042Z" fill="url(#paint0_linear_6818_37772)"/>
<path d="M17.3335 5.5376H45.6812L67.1313 26.8901L67.1333 69.9995C67.1334 72.4664 65.1334 74.4662 62.6665 74.4663H17.3345C14.8679 74.4661 12.8679 72.4671 12.8677 70.0005L12.8667 10.0044C12.8666 7.53748 14.8666 5.5376 17.3335 5.5376Z" stroke="url(#paint1_linear_6818_37772)" stroke-width="0.4"/>
</g>
<g filter="url(#filter1_i_6818_37772)">
<rect x="20.6665" y="63.3334" width="40" height="3.33333" rx="1.66667" fill="#E7E7E7"/>
</g>
<rect x="20.5332" y="63.2" width="40.2667" height="3.6" rx="1.8" stroke="url(#paint2_linear_6818_37772)" stroke-width="0.266667"/>
<g filter="url(#filter2_dii_6818_37772)">
<path d="M23.3335 27.3334C23.3335 25.1242 25.1244 23.3334 27.3335 23.3334H52.6668C54.876 23.3334 56.6668 25.1242 56.6668 27.3334V52C56.6668 54.2092 54.876 56 52.6668 56H27.3335C25.1244 56 23.3335 54.2092 23.3335 52V27.3334Z" fill="url(#paint3_linear_6818_37772)"/>
<path d="M23.3335 27.3334C23.3335 25.1242 25.1244 23.3334 27.3335 23.3334H52.6668C54.876 23.3334 56.6668 25.1242 56.6668 27.3334V52C56.6668 54.2092 54.876 56 52.6668 56H27.3335C25.1244 56 23.3335 54.2092 23.3335 52V27.3334Z" stroke="url(#paint4_linear_6818_37772)" stroke-width="0.333333"/>
</g>
<g filter="url(#filter3_d_6818_37772)">
<g filter="url(#filter4_dii_6818_37772)">
<path d="M47.3784 32.7888C46.6035 31.967 45.3146 31.9308 44.4996 32.7079L39.9825 37.0145L35.8586 32.6408C35.0838 31.8191 33.7949 31.7828 32.9798 32.5599C32.1647 33.337 32.1321 34.6332 32.9069 35.455L37.0308 39.8287L32.6929 43.9645C31.8778 44.7416 31.8452 46.0377 32.6201 46.8595C33.3949 47.6813 34.6838 47.7175 35.4989 46.9404L39.8368 42.8046L44.1308 47.3587C44.9056 48.1805 46.1945 48.2167 47.0096 47.4396C47.8247 46.6625 47.8573 45.3663 47.0824 44.5446L42.7885 39.9905L47.3055 35.6838C48.1206 34.9067 48.1532 33.6106 47.3784 32.7888Z" fill="url(#paint5_linear_6818_37772)"/>
<path d="M33.0497 32.6328C33.7761 31.9404 34.8982 31.9276 35.6422 32.5723L35.7854 32.71L39.9093 37.0839L39.9789 37.1582L40.0528 37.0878L44.5699 32.7812C45.2962 32.0889 46.4174 32.0761 47.1614 32.7207L47.3046 32.8584C47.9952 33.5909 48.0115 34.7196 47.3718 35.4668L47.2357 35.6103L42.7187 39.9169L42.6457 39.9862L42.7154 40.0595L47.009 44.6141C47.6994 45.3465 47.7157 46.4754 47.0763 47.2225L46.9402 47.3661C46.2139 48.0585 45.0917 48.0719 44.3477 47.4276L44.2045 47.2899L39.9099 42.7352L39.8402 42.661L39.7673 42.7313L35.4292 46.867C34.7027 47.5596 33.5807 47.5724 32.8367 46.9276L32.6935 46.7899C32.0031 46.0575 31.9868 44.9286 32.6263 44.1815L32.7623 44.038L37.1005 39.9023L37.1734 39.8329L37.1037 39.7597L32.9799 35.3857C32.2897 34.6534 32.2736 33.5244 32.9126 32.7773L33.0497 32.6328Z" stroke="url(#paint6_linear_6818_37772)" stroke-width="0.202014"/>
</g>
</g>
<g filter="url(#filter5_d_6818_37772)">
<mask id="path-8-inside-1_6818_37772" fill="white">
<path d="M67.3325 27.5033C67.3325 27.5033 67.1783 26.4545 65.5356 26.4545H67.3325V27.5033ZM46.106 5.33337L67.3315 26.4506H50.9995C48.4473 26.4505 46.3686 24.4 46.3335 21.848L46.1108 5.68689V7.12634C46.1108 5.50472 45.0755 5.33994 45.0581 5.33728H46.106V5.33337Z"/>
</mask>
<path d="M67.3325 27.5033C67.3325 27.5033 67.1783 26.4545 65.5356 26.4545H67.3325V27.5033ZM46.106 5.33337L67.3315 26.4506H50.9995C48.4473 26.4505 46.3686 24.4 46.3335 21.848L46.1108 5.68689V7.12634C46.1108 5.50472 45.0755 5.33994 45.0581 5.33728H46.106V5.33337Z" fill="url(#paint7_linear_6818_37772)"/>
<path d="M67.3325 27.5033L66.7931 27.5826L67.8777 27.5033H67.3325ZM65.5356 26.4545V25.9093L65.5356 26.9997L65.5356 26.4545ZM67.3325 26.4545H67.8777V25.9093H67.3325V26.4545ZM46.106 5.33337L46.4905 4.94688L45.5608 4.02193V5.33337H46.106ZM67.3315 26.4506V26.9957H68.6525L67.7161 26.0641L67.3315 26.4506ZM50.9995 26.4506L50.9995 26.9957H50.9995V26.4506ZM46.3335 21.848L46.8786 21.8405L46.8786 21.8405L46.3335 21.848ZM46.1108 5.68689L46.656 5.67938L45.5657 5.68689H46.1108ZM45.0581 5.33728V4.7921L44.9759 5.87623L45.0581 5.33728ZM46.106 5.33728V5.88246H46.6511V5.33728H46.106ZM67.3325 27.5033C67.8719 27.424 67.8718 27.4236 67.8718 27.4232C67.8718 27.423 67.8717 27.4226 67.8717 27.4223C67.8716 27.4217 67.8715 27.4212 67.8714 27.4205C67.8712 27.4193 67.871 27.418 67.8708 27.4167C67.8704 27.414 67.8699 27.411 67.8693 27.4077C67.8682 27.4012 67.8668 27.3936 67.8651 27.385C67.8616 27.3678 67.8569 27.3464 67.8505 27.3216C67.8376 27.272 67.8178 27.2076 67.7875 27.1334C67.727 26.9852 67.6236 26.795 67.4493 26.6076C67.0867 26.2178 66.484 25.9093 65.5357 25.9093L65.5356 26.4545L65.5356 26.9997C66.2299 26.9997 66.5257 27.2156 66.6509 27.3502C66.7204 27.425 66.7582 27.497 66.778 27.5454C66.7879 27.5696 66.7929 27.5871 66.7949 27.5949C66.7959 27.5987 66.7961 27.5999 66.7957 27.598C66.7955 27.5971 66.7952 27.5954 66.7948 27.5929C66.7945 27.5916 66.7943 27.5901 66.794 27.5884C66.7939 27.5875 66.7937 27.5866 66.7936 27.5857C66.7935 27.5852 66.7934 27.5847 66.7934 27.5842C66.7933 27.5839 66.7933 27.5835 66.7933 27.5834C66.7932 27.583 66.7931 27.5826 67.3325 27.5033ZM65.5356 26.4545V26.9997H67.3325V26.4545V25.9093H65.5356V26.4545ZM67.3325 26.4545H66.7873V27.5033H67.3325H67.8777V26.4545H67.3325ZM46.106 5.33337L45.7214 5.71986L66.947 26.8371L67.3315 26.4506L67.7161 26.0641L46.4905 4.94688L46.106 5.33337ZM67.3315 26.4506V25.9054H50.9995V26.4506V26.9957H67.3315V26.4506ZM50.9995 26.4506L50.9995 25.9054C48.7455 25.9053 46.9096 24.0944 46.8786 21.8405L46.3335 21.848L45.7884 21.8555C45.8276 24.7056 48.1491 26.9956 50.9995 26.9957L50.9995 26.4506ZM46.3335 21.848L46.8786 21.8405L46.656 5.67938L46.1108 5.68689L45.5657 5.6944L45.7884 21.8555L46.3335 21.848ZM46.1108 5.68689H45.5657V7.12634H46.1108H46.656V5.68689H46.1108ZM46.1108 7.12634L46.656 7.12634C46.656 6.18846 46.3509 5.58914 45.9635 5.22662C45.5923 4.87922 45.2087 4.80876 45.1403 4.79833L45.0581 5.33728L44.9759 5.87623C44.925 5.86846 45.0678 5.88172 45.2185 6.02278C45.3531 6.14872 45.5657 6.4426 45.5657 7.12634L46.1108 7.12634ZM45.0581 5.33728V5.88246H46.106V5.33728V4.7921H45.0581V5.33728ZM46.106 5.33728H46.6511V5.33337H46.106H45.5608V5.33728H46.106Z" fill="url(#paint8_linear_6818_37772)" mask="url(#path-8-inside-1_6818_37772)"/>
</g>
<g filter="url(#filter6_dii_6818_37772)">
<circle cx="29.3335" cy="64.6666" r="4" fill="url(#paint9_linear_6818_37772)"/>
<circle cx="29.3335" cy="64.6666" r="4" stroke="url(#paint10_linear_6818_37772)" stroke-width="0.0533333"/>
</g>
<g filter="url(#filter7_i_6818_37772)">
<circle cx="29.3333" cy="64.6667" r="2.33333" fill="url(#paint11_linear_6818_37772)"/>
</g>
<circle cx="29.3333" cy="64.6667" r="2.4" stroke="url(#paint12_linear_6818_37772)" stroke-width="0.133333"/>
<defs>
<filter id="filter0_dii_6818_37772" x="11.9998" y="5.00407" width="55.9998" height="70.996" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.666667"/>
<feGaussianBlur stdDeviation="0.333333"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6818_37772"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6818_37772" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.06667" dy="1.06667"/>
<feGaussianBlur stdDeviation="0.333333"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_6818_37772"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="0.166667"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.879167 0 0 0 0 0.879167 0 0 0 0 0.879167 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_6818_37772" result="effect3_innerShadow_6818_37772"/>
</filter>
<filter id="filter1_i_6818_37772" x="20.3999" y="63.0667" width="40.5332" height="4.20003" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.952269"/>
<feGaussianBlur stdDeviation="0.166667"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.729167 0 0 0 0 0.729167 0 0 0 0 0.729167 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_6818_37772"/>
</filter>
<filter id="filter2_dii_6818_37772" x="22.5003" y="22.5001" width="34.9998" height="34.6667" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.333333"/>
<feGaussianBlur stdDeviation="0.333333"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.831152 0 0 0 0 0.898691 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6818_37772"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6818_37772" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.333333"/>
<feGaussianBlur stdDeviation="0.333333"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.520833 0 0 0 0 0.683627 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_6818_37772"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.333333" dy="-0.666667"/>
<feGaussianBlur stdDeviation="0.333333"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0826042 0 0 0 0 0.314531 0 0 0 0 0.7625 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_6818_37772" result="effect3_innerShadow_6818_37772"/>
</filter>
<filter id="filter3_d_6818_37772" x="31.6818" y="32" width="16.635" height="16.7551" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.377778"/>
<feGaussianBlur stdDeviation="0.188889"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.161944 0 0 0 0 0.407217 0 0 0 0 0.883333 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6818_37772"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6818_37772" result="shape"/>
</filter>
<filter id="filter4_dii_6818_37772" x="31.304" y="31.8489" width="18.9016" height="19.1728" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.755556" dy="1.51111"/>
<feGaussianBlur stdDeviation="0.755556"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.141176 0 0 0 0 0.392157 0 0 0 0 0.878431 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6818_37772"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6818_37772" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.377778" dy="0.377778"/>
<feGaussianBlur stdDeviation="0.113333"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_6818_37772"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.377778" dy="-0.377778"/>
<feGaussianBlur stdDeviation="0.0755556"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.879167 0 0 0 0 0.879167 0 0 0 0 0.879167 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_6818_37772" result="effect3_innerShadow_6818_37772"/>
</filter>
<filter id="filter5_d_6818_37772" x="43.471" y="5.33337" width="24.8138" height="25.3442" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.317423" dy="1.90454"/>
<feGaussianBlur stdDeviation="0.634846"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6818_37772"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6818_37772" result="shape"/>
</filter>
<filter id="filter6_dii_6818_37772" x="24.64" y="60.3067" width="9.38704" height="9.38656" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.333333"/>
<feGaussianBlur stdDeviation="0.333333"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.705801 0 0 0 0 0.705801 0 0 0 0 0.720866 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6818_37772"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6818_37772" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.106667" dy="0.0666667"/>
<feGaussianBlur stdDeviation="0.0533333"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.658333 0 0 0 0 0.775 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_6818_37772"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.0533333" dy="-0.106667"/>
<feGaussianBlur stdDeviation="0.0533333"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0750695 0 0 0 0 0.314483 0 0 0 0 0.783333 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_6818_37772" result="effect3_innerShadow_6818_37772"/>
</filter>
<filter id="filter7_i_6818_37772" x="26.7334" y="62.0667" width="5.06644" height="5.06668" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-0.133333" dy="-0.133333"/>
<feGaussianBlur stdDeviation="0.1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.651997 0 0 0 0 0.692105 0 0 0 0 0.770833 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_6818_37772"/>
</filter>
<linearGradient id="paint0_linear_6818_37772" x1="57.4682" y1="13.8376" x2="11.2869" y2="77.4119" gradientUnits="userSpaceOnUse">
<stop stop-color="#FAFAFA"/>
<stop offset="0.855332" stop-color="#F4F4F4"/>
</linearGradient>
<linearGradient id="paint1_linear_6818_37772" x1="34.8065" y1="5.3374" x2="25.1096" y2="72.354" gradientUnits="userSpaceOnUse">
<stop stop-color="#E8E8EB"/>
<stop offset="0.192009" stop-color="#DEDEDE"/>
<stop offset="0.703503" stop-color="#D8D8D8"/>
<stop offset="0.962692" stop-color="#C3C3C7"/>
</linearGradient>
<linearGradient id="paint2_linear_6818_37772" x1="42.9998" y1="60" x2="43.0511" y2="68.2011" gradientUnits="userSpaceOnUse">
<stop offset="0.272911" stop-color="#B1B1B1"/>
<stop offset="0.5441" stop-color="#DADADD"/>
<stop offset="1" stop-color="#DFDFDF"/>
</linearGradient>
<linearGradient id="paint3_linear_6818_37772" x1="34.9751" y1="22.0486" x2="54.5722" y2="48.6548" gradientUnits="userSpaceOnUse">
<stop stop-color="#6398FF"/>
<stop offset="1" stop-color="#216BFA"/>
</linearGradient>
<linearGradient id="paint4_linear_6818_37772" x1="35.9285" y1="32.7362" x2="48.6083" y2="51.3282" gradientUnits="userSpaceOnUse">
<stop stop-color="#97BBFF"/>
<stop offset="1" stop-color="#0B4FD1"/>
</linearGradient>
<linearGradient id="paint5_linear_6818_37772" x1="34.8764" y1="33.5404" x2="47.7724" y2="45.6236" gradientUnits="userSpaceOnUse">
<stop stop-color="#EAEAEA"/>
<stop offset="0.855332" stop-color="#F5F5F5"/>
</linearGradient>
<linearGradient id="paint6_linear_6818_37772" x1="33.6093" y1="30.8669" x2="42.4408" y2="46.8651" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.962692" stop-color="#D5D5D5"/>
</linearGradient>
<linearGradient id="paint7_linear_6818_37772" x1="59.8629" y1="14.6482" x2="49.6427" y2="27.757" gradientUnits="userSpaceOnUse">
<stop offset="0.162459" stop-color="#F2F2F2"/>
<stop offset="0.738" stop-color="white"/>
</linearGradient>
<linearGradient id="paint8_linear_6818_37772" x1="43.3606" y1="29.2793" x2="53.6201" y2="19.027" gradientUnits="userSpaceOnUse">
<stop stop-color="#E2E2E2"/>
<stop offset="1" stop-color="#9C9C9C" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint9_linear_6818_37772" x1="28.1275" y1="60.352" x2="32.9547" y2="66.7747" gradientUnits="userSpaceOnUse">
<stop stop-color="#6397FD"/>
<stop offset="1" stop-color="#216BFA"/>
</linearGradient>
<linearGradient id="paint10_linear_6818_37772" x1="28.3563" y1="62.9694" x2="31.484" y2="67.4637" gradientUnits="userSpaceOnUse">
<stop stop-color="#6C9EFF"/>
<stop offset="1" stop-color="#154CB7"/>
</linearGradient>
<linearGradient id="paint11_linear_6818_37772" x1="28.6298" y1="62.1498" x2="31.4457" y2="65.8964" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint12_linear_6818_37772" x1="28.7633" y1="63.6766" x2="30.5878" y2="66.2983" gradientUnits="userSpaceOnUse">
<stop stop-color="#F4F4F4"/>
<stop offset="1" stop-color="#B0C4F0"/>
</linearGradient>
</defs>
</svg>
