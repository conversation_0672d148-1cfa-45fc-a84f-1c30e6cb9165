import localFont from 'next/font/local'

export const robotoSlab = localFont({
  src: [
    {
      path: '../assets/fonts/RobotoSlab-Thin.ttf',
      weight: '100',
      style: 'normal',
    },
    {
      path: '../assets/fonts/RobotoSlab-ExtraLight.ttf',
      weight: '200',
      style: 'normal',
    },
    {
      path: '../assets/fonts/RobotoSlab-Light.ttf',
      weight: '300',
      style: 'normal',
    },
    {
      path: '../assets/fonts/RobotoSlab-Regular.ttf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../assets/fonts/RobotoSlab-Medium.ttf',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../assets/fonts/RobotoSlab-SemiBold.ttf',
      weight: '600',
      style: 'normal',
    },
    {
      path: '../assets/fonts/RobotoSlab-Bold.ttf',
      weight: '700',
      style: 'normal',
    },
    {
      path: '../assets/fonts/RobotoSlab-ExtraBold.ttf',
      weight: '800',
      style: 'normal',
    },
    {
      path: '../assets/fonts/RobotoSlab-Black.ttf',
      weight: '900',
      style: 'normal',
    },
  ],
  variable: '--font-roboto-slab',
  display: 'swap',
}) 