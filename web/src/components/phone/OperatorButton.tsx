import { useState } from 'react';
import { Button } from '../ui/button';

const OperatorButton = ({
  callback,
  icon,
}: {
  callback: () => Promise<void> | void;
  icon: React.ReactNode;
}) => {
  const [loading, setLoading] = useState(false);

  const handleClick = async () => {
    try {
      setLoading(true);
      await callback();
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button variant="outline" onClick={handleClick} disabled={loading}>
      {loading ? (
        <div className="w-4 h-4 rounded-full border-2 border-gray-300 border-t-black animate-spin" />
      ) : (
        icon
      )}
    </Button>
  );
};

export default OperatorButton;
