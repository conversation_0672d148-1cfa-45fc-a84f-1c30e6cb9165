"use client";
import React from 'react';
import { UserMessage } from './ChatMessage';
import { Message } from '@/hooks/useCloudAgent';
import ThinkingMessageComponent from './ThinkingMessage';

interface MessageListProps {
  messages: Message[];
}

const MessageList: React.FC<MessageListProps> = ({ messages }) => {
  return (
    <div className="space-y-2">
      {messages.map((message, idx) => {
        // 如果是用户消息，直接显示
        if (message.isUser) {
          return <UserMessage key={message.id} message={message} />;
        } else {
          return <ThinkingMessageComponent key={`thinking-${message.id}`} messageId={message.id} message={message} />;
        }
      }, [])}
    </div>
  );
};

export default MessageList;
