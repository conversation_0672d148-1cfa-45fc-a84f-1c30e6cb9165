import React from 'react';
import TimeoutPanel from './TimeoutPanel';

interface TimeoutViewProps {
  timeoutState: string;
  onRetry: () => void;
}

const TimeoutView: React.FC<TimeoutViewProps> = ({ timeoutState, onRetry }) => {
  return (
    <div className="absolute inset-0 flex items-center justify-center">
      <TimeoutPanel type={timeoutState as 'experienceTimeout'} onRetry={onRetry} />
    </div>
  );
};

export default TimeoutView; 