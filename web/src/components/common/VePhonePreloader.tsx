'use client';

import { useEffect } from 'react';

export function VePhonePreloader() {
  useEffect(() => {
    // 预加载 VePhone SDK
    const preloadSDK = async () => {
      try {
        const { default: UMDLoader } = await import('@/lib/vePhone/loader');
        const loader = UMDLoader.getInstance();
        await loader.loadVePhoneSDK();
        console.log('VePhone SDK preloaded successfully');
      } catch (error) {
        console.warn('Failed to preload VePhone SDK:', error);
      }
    };

    preloadSDK();
  }, []);

  return null; // 这个组件不渲染任何内容
} 