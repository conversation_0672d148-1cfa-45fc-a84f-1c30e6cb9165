import { cn } from "@/lib/utils";
import { PanelResizeHandle } from "react-resizable-panels";

// 自定义拖拽手柄组件
function ResizeHandle({ className = "" }: { className?: string }) {
  return (
    <PanelResizeHandle className={cn(
      "PanelResizeHandle w-2 mx-1 group relative cursor-col-resize",
      className
    )}>
      <div className="absolute top-0 bottom-0 left-0 right-0 flex items-center justify-center">
        <div className="w-1 h-10 bg-gray-300 rounded transition-colors group-hover:bg-blue-400 group-active:bg-blue-500" />
      </div>
    </PanelResizeHandle>
  );
}

export default ResizeHandle;