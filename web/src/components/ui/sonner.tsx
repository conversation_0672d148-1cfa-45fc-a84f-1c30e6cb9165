"use client"

import { useTheme } from "next-themes"
import { Toaster as Son<PERSON>, ToasterProps } from "sonner"
import Image from "next/image"
import WarningIcon from "@/assets/toast-warning.svg"

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme()

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      style={
        {
          "--normal-bg": "var(--popover)",
          "--normal-text": "var(--popover-foreground)",
          "--normal-border": "var(--border)",
        } as React.CSSProperties
      }
      position="top-center"
      duration={10000}
      toastOptions={{
        style: {
          padding: '8px 16px',
          fontSize: '14px',
          lineHeight: '20px',
          color: '#0C0D0E',
          gap: '4px',
        },
      }}
      icons={{
        warning: <Image src={WarningIcon} alt="warning" width={16} height={16} />,
      }}
      offset={{
        top: '32px'
      }}
      {...props}
    />
  )
}

export { Toaster }
