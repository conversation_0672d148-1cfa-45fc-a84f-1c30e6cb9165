import logger from '@/lib/vePhone/log';

export function catchLog() {
   
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor,
  ) {
    const className = target?.constructor?.name;
    const originalMethod = descriptor?.value;

    // 修改方法行为
    descriptor.value = async function (...args: any[]) {
      logger.info(`${className},${propertyKey}:start`, args);
      try {
        const result = await originalMethod.apply(this, args);
        logger.info(`${className},${propertyKey}:success`, result);
        return result;
      } catch (error) {
        logger.error(`${className},${propertyKey}:failed`, error);
        throw error;
      }
    };

    return descriptor;
  };
}
