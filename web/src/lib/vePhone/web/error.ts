export class VePhoneError {
  errorCode: string;
  message: string;
  originalErrorCode: number;
  originalMessage: string;

  constructor(error: {
    errorCode: string;
    message: string;
    originalErrorCode: number;
    originalMessage: string;
  }) {
    this.errorCode = error.errorCode;
    this.message = error.message;
    this.originalErrorCode = error.originalErrorCode;
    this.originalMessage = error.originalMessage;
  }
}
