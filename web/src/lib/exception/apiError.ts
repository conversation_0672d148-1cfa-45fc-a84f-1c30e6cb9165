export class APIError extends Error {
  static readonly TYPE = 'API_ERROR';
  readonly type = APIError.TYPE;

  get code(): number {
    return this._code;
  }

  get originMsg(): string {
    return this._message;
  }

  constructor(private _code: number, private _message: string) {
    const ERROR_MESSAGES: { [key: number]: string } = {
      [2001]: '当前账号已占用实例体验中，请稍后再试',
    }
    super(ERROR_MESSAGES[_code] || _message);
  }
}
