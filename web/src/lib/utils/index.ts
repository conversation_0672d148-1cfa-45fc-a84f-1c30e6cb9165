export * from './safeJSONParse';
export * from './time';
export * from './delay';
export * from './css';

import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * 构建带 token 的 URL 的辅助函数
 * 用于在页面跳转时保留 URL 中的 token 参数
 * @param path 目标路径
 * @param searchParams 可选的 URLSearchParams 对象，如果不提供则从当前页面读取
 * @returns 带 token 的完整路径
 */
export function buildUrlWithToken(path: string, searchParams?: URLSearchParams): string {
  let urlParams: URLSearchParams;
  
  if (searchParams) {
    urlParams = searchParams;
  } else if (typeof window !== 'undefined') {
    urlParams = new URLSearchParams(window.location.search);
  } else {
    return path;
  }
  
  const token = urlParams.get('token');
  if (token) {
    const url = new URL(path, typeof window !== 'undefined' ? window.location.origin : 'http://localhost');
    url.searchParams.set('token', token);
    return url.pathname + url.search;
  }
  return path;
}

/**
 * 获取当前页面 URL 中的 token 参数
 * @returns token 字符串或 null
 */
export function getCurrentToken(): string | null {
  if (typeof window !== 'undefined') {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('token');
  }
  return null;
}
