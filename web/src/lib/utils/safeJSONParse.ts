export function safeJSONParse(jsonStr?: string) {
  if (!jsonStr) {
    return null;
  }
  try {
    return JSON.parse(jsonStr);
  } catch (error) {
    console.error('safeJSONParse error', error);
    return null;
  }
}

export function safeJSONParseWithError(jsonStr?: string) {
  const result = safeJSONParse(jsonStr);
  if (!result) {
    throw new Error(`safeJSONParseWithError error: ${jsonStr}`);
  }
  return result;
}
