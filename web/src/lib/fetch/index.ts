import { toast } from "sonner";
import { SessionAffinityManager } from "./session";
import { toastRedirect } from "./redirect";
import { buildUrlWithToken } from "../utils";

const beforeFetchSessionHeaders = () => {
  const faasInstanceName = SessionAffinityManager.getFaasInstanceName();
  if (faasInstanceName) {
    return {
      'x-agent-faas-instance-name': faasInstanceName,
    }
  }
  return null;
}

const afterFetchSession = (response: Response) => {
  const faasInstanceName = SessionAffinityManager.getFaasInstanceName();
  const responseFaasInstanceName = response.headers.get('x-agent-faas-instance-name');
  if (responseFaasInstanceName && responseFaasInstanceName !== faasInstanceName) {
    SessionAffinityManager.setFaasInstanceName(responseFaasInstanceName);
    console.log('存储新的FaaS实例名称:', responseFaasInstanceName);
  }
}

const handleErrorResponse = async (response: Response) => {
  const data = await response.json()
  if (data?.error && data?.error?.code !== 0) {
    if (response.status === 401) {
      toastRedirect()
      return
    }
    if (response.status === 200 && data?.error?.code === 403) {
      toast.warning(data?.error?.message || "会话不存在，请重新开始会话")
      sessionStorage.clear()
      setTimeout(() => {
        // 保留 token 参数进行页面跳转
        window.location.replace(buildUrlWithToken('/'));
      }, 1500)
      return;
    }
    toast.warning(data.error.message)
    return;
  }
  return data;
}

const fetchAPI = async (url: string, options: RequestInit) => {
  try {

    const headers = beforeFetchSessionHeaders();

    // API Auth Key Token - 将 URL token 参数传递给 API
    const urlParams = new URLSearchParams(window.location.search);
    const urlToken = urlParams.get('token');
    const apiUrl = urlToken ? `${url}?token=${encodeURIComponent(urlToken)}` : url;

    const response = await fetch(apiUrl, {
      ...options,
      headers: {
        ...(headers || {}),
        ...options.headers,
      },
    });
    // 检查响应中是否包含新的FaaS实例名称
    afterFetchSession(response);
    const data = await handleErrorResponse(response)
    return data
  } catch (error) {
    if (error instanceof Error) {
      toast.warning(error.message)
    } else {
      toast.warning("未知错误")
    }
    return;
  }

}

const fetchSSE = async (url: string, options: RequestInit) => {
  try {
    const headers = beforeFetchSessionHeaders();
    // API Auth Key Token - 将 URL token 参数传递给 SSE API
    const urlParams = new URLSearchParams(window.location.search);
    const urlToken = urlParams.get('token');
    const apiUrl = urlToken ? `${url}?token=${encodeURIComponent(urlToken)}` : url;
    const response = await fetch(apiUrl, {
      ...options,
      headers: {
        ...(headers || {}),
        ...options.headers,
      },
      signal: options.signal,
    });
    afterFetchSession(response);
    if (response.headers.get('Content-Type') === 'text/event-stream') {
      if (!response.ok || !response.body) {
        const { error } = (await response.json().catch(() => ({ error: { message: '未知错误' } }))) || { error: { message: '未知错误' } };
        toast.warning(error.message);
        throw new Error(`HTTP错误: ${response.status}`);
      }
      return response.body
    }
    const data = await handleErrorResponse(response)
    return data
  } catch (error) {
    console.log(error)
    // 检查是否为中止错误，如果是则不显示错误提示
    if (error instanceof DOMException && error.name === 'AbortError') {
      console.log('请求已被中止');
      return;
    }
    if (error instanceof Error) {
      toast.warning(error.message)
    } else {
      toast.warning("未知错误")
    }
    return;
  }
}

export { fetchAPI, fetchSSE };
