// Session亲和性管理工具

const FAAS_INSTANCE_KEY = 'mobile_use:agent_faas_instance_name';

export class SessionAffinityManager {
  /**
   * 获取当前存储的FaaS实例名称
   */
  static getFaasInstanceName(): string | null {
    if (typeof window !== 'undefined') {
      return sessionStorage.getItem(FAAS_INSTANCE_KEY);
    }
    return null;
  }

  /**
   * 存储FaaS实例名称
   */
  static setFaasInstanceName(instanceName: string): void {
    if (typeof window !== 'undefined') {
      sessionStorage.setItem(FAAS_INSTANCE_KEY, instanceName);
      console.log('✅ Session亲和性: 存储FaaS实例名称:', instanceName);
    }
  }

  /**
   * 清除FaaS实例名称
   */
  static clearFaasInstanceName(): void {
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem(FAAS_INSTANCE_KEY);
      console.log('🗑️ Session亲和性: 清除FaaS实例名称');
    }
  }

  /**
   * 检查是否有活跃的session亲和性
   */
  static hasActiveSession(): boolean {
    return this.getFaasInstanceName() !== null;
  }

  /**
   * 重置session（清除所有相关数据）
   */
  static resetSession(): void {
    this.clearFaasInstanceName();
    // 可以在这里添加其他需要清除的session相关数据
  }
}