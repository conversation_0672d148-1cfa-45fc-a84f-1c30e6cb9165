import type { Metadata } from 'next';
import { Toaster } from '@/components/ui/sonner';
import './globals.css';
import { VePhonePreloader } from '@/components/common/VePhonePreloader';

export const metadata: Metadata = {
  title: 'Mobile Use',
  description: 'Mobile Use Agent is All You Need.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="antialiased">
      <VePhonePreloader />
      <main
          style={{
            background: 'linear-gradient(77.86deg, #EBF7FF -3.23%, #E6EEFF 51.11%, #F8F0FF 98.65%)',
          }}
        >
          {children}
        </main>
        <Toaster />
      </body>
    </html>
  );
}
