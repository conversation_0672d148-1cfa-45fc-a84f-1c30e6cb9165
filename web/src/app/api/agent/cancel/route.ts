import * as url from "url"
import { Api<PERSON>and<PERSON>, withMiddleware } from "../../_utils/middleware";
import { fetchServer } from "@/app/api/_utils/fetch";


const target = url.resolve(process.env.CLOUD_AGENT_BASE_URL || "", 'api/v1/agent/cancel')

const _post: ApiHandler = async (request: Request, middlewareResult) => {
  const { thread_id } = await request.json();
  const response = await fetchServer(target, middlewareResult, { thread_id }, 'POST');
  return response;
}

export const POST = withMiddleware(_post);
