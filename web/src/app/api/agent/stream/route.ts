import { ApiHandler, withMiddleware } from "../../_utils/middleware";
import * as url from "url";
import { fetchServer } from "../../_utils/fetch";

const target = url.resolve(process.env.CLOUD_AGENT_BASE_URL || "", 'api/v1/agent/stream')

const _post: ApiHandler = async (request: Request, middlewareResult) => {
  const { message, thread_id, pod_id } = await request.json();
  const response = await fetchServer(target, middlewareResult, { message, thread_id, pod_id, is_stream: true }, 'POST');
  return response;
};

export const POST = withMiddleware(_post);