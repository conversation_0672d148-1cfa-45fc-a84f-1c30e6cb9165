import { APIError } from "../../../lib/exception/apiError";

export async function handleVEFaaSError(errorBody: Record<string, any>, status: number) {
  // 特殊处理 500 状态码的情况
  if (status === 500) {
    // 检查是否是特定的内部系统错误
    if (["internal_system_error", "internal_proxy_error"].includes(errorBody?.error_code)) {
      // 返回 403 重定向
      throw new APIError(403, "会话不存在，请重新开始会话");
    }
  }
  if (status === 401) {
    throw new APIError(401, "请提供网站访问 Token 参数");
  }
}