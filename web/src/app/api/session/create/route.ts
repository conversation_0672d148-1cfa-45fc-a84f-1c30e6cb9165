import * as url from "url"
import { type ApiHandler, withMiddleware } from "../../_utils/middleware";
import { fetchServer } from "../../_utils/fetch";

const target = url.resolve(process.env.CLOUD_AGENT_BASE_URL || "", 'api/v1/session/create')

// 处理创建会话的请求
const _post: ApiHandler = async (request: Request, middlewareResult) => {
  const { thread_id, product_id, pod_id } = await request.json();
  const response = await fetchServer(
    target,
    middlewareResult,
    { thread_id, product_id, pod_id },
    'POST',
    { withUserInfo: true }
  )
  return response
};

// 导出包装后的处理函数
export const POST = withMiddleware(_post);

