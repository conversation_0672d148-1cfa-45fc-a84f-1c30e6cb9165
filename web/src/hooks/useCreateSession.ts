import { SessionBackendResponse } from "@/types";
import { useCloudAgent } from "./useCloudAgent";
import { fetchAPI } from '@/lib/fetch';
import { SessionDataAtom } from "@/app/atom";
import { useSet<PERSON>tom } from "jotai";
import { changeAgentChatThreadId } from '@/lib/cloudAgent';

const useCreateSessionAPI = () => {
  const cloudAgent = useCloudAgent();
  const setSessionData = useSetAtom(SessionDataAtom);

  const createSession = async (
    productId?: string,
    podId?: string,
  ) => {
    if (!cloudAgent) {
      return null;
    }

    const _productId = productId || cloudAgent.productId;
    const _podId = podId || cloudAgent.podId;

    if (!_productId || !_podId) {
      return null;
    }

    const data = (await fetchAPI('/api/session/create', {
      method: 'POST',
      body: JSON.stringify({ thread_id: cloudAgent.threadId, product_id: _productId, pod_id: _podId }),
    })) as SessionBackendResponse;

    if (data) {
      // 将会话数据存储到全局状态
      cloudAgent.setThreadId(data.thread_id);
      cloudAgent.setProductPodId(data.pod.product_id, data.pod.pod_id);
      setSessionData(data);
      changeAgentChatThreadId(data.chat_thread_id);

      return data;
    }

    return null;
  };

  return { createSession };
};

export default useCreateSessionAPI;