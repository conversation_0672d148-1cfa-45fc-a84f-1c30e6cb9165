import { useEffect, useState } from 'react';

export const usePanelResize = () => {
  const [leftPanelMinSize, setLeftPanelMinSize] = useState(30);
  const [rightPanelMinSize, setRightPanelMinSize] = useState(20);

  // 面板默认尺寸

  // 响应式布局设置
  useEffect(() => {
    const handleResize = () => {
      setRightPanelMinSize(Math.floor((400 / window.innerWidth) * 100));
      setLeftPanelMinSize(Math.floor((600 / window.innerWidth) * 100));
    };

    // 初始化时执行一次
    handleResize();

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize);

    // 清理函数
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return {
    leftPanelMinSize,
    rightPanelMinSize,
  };
};
