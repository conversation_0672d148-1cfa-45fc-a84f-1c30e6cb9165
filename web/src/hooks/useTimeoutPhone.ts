import { use<PERSON>tom, useAtomValue, useSet<PERSON>tom } from "jotai";
import { useRef } from "react";
import { useCloudAgent } from "./useCloudAgent";
import { TimeoutStateAtom, CountdownAtom, VePhone<PERSON>tom, SessionData<PERSON>tom, StartTimeAtom } from "@/app/atom";



const useTimeoutPhone = () => {
  const [vePhone] = useAtom(VePhoneAtom);
  const sessionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const countdownTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const setTimeoutState = useSetAtom(TimeoutStateAtom);
  const setSessionData = useSetAtom(SessionDataAtom);
  const setStartTime = useSetAtom(StartTimeAtom);
  const setCountdownTime = useSetAtom(CountdownAtom);
  const cloudAgent = useCloudAgent()

  const clear = () => {
    if (countdownTimeoutRef.current) {
      clearInterval(countdownTimeoutRef.current)
      countdownTimeoutRef.current = null
    }
    if (sessionTimeoutRef.current) {
      clearTimeout(sessionTimeoutRef.current);
      sessionTimeoutRef.current = null
    }
  }

  const initCountdown = (countdownTime: number) => {
    clear()
    console.log('initCountdown', countdownTime)
    // 记录开始时间
    setStartTime(performance.now());
    setCountdownTime(countdownTime);
    countdownTimeoutRef.current = setInterval(() => {
      console.log('countdownTimeoutRef')
      setCountdownTime(prev => (prev > 0 ? prev - 1 : 0));
    }, 1000);
    sessionTimeoutRef.current = setTimeout(handleEnd, countdownTime * 1000);
  }

  const handleEnd = async () => {
    // 断掉 sse
    if (cloudAgent?.cancel) {
      await cloudAgent?.cancel?.()
    }
    // 重置 vePhone
    vePhone?.reset();
    // 设置 ui 状态
    setTimeoutState('experienceTimeout');
    setSessionData(null);
    // 清除开始时间
    setStartTime(null);
  }

  return initCountdown
}


export const useTimeoutState = () => {
  const countdownTime = useAtomValue(CountdownAtom);
  const timeoutState = useAtomValue(TimeoutStateAtom);
  const startTime = useAtomValue(StartTimeAtom);
  return {
    countdownTime,
    timeoutState,
    startTime
  }
}

export default useTimeoutPhone;