# 批量执行CAGUI Instructions

这个工具允许您批量执行CAGUI CSV文件中的instructions，支持范围控制、状态重置、消息隔离等功能。

## 功能特性

- ✅ 串行执行instructions
- ✅ 支持起始和结束条数控制
- ✅ 每个instruction执行前自动返回主页面
- ✅ 独立的thread_id确保消息隔离
- ✅ 完整的执行结果记录和错误处理
- ✅ 支持重试机制
- ✅ 详细的日志记录
- ✅ 多种格式的结果输出（JSON、CSV）

## 快速开始

### 1. 基本使用

```bash
cd mobile_agent
python batch_run.py \
  --csv cagui.csv \
  --start 1 \
  --end 10 \
  --pod-id YOUR_POD_ID \
  --auth-token YOUR_AUTH_TOKEN \
  --product-id YOUR_PRODUCT_ID \
  --tos-bucket YOUR_TOS_BUCKET \
  --tos-region YOUR_TOS_REGION \
  --tos-endpoint YOUR_TOS_ENDPOINT
```

### 2. 使用自定义配置

```bash
python batch_run.py \
  --csv cagui.csv \
  --start 5 \
  --end 20 \
  --config batch_config_example.json \
  --pod-id YOUR_POD_ID \
  --auth-token YOUR_AUTH_TOKEN \
  --product-id YOUR_PRODUCT_ID \
  --tos-bucket YOUR_TOS_BUCKET \
  --tos-region YOUR_TOS_REGION \
  --tos-endpoint YOUR_TOS_ENDPOINT
```

### 3. 执行到文件末尾

```bash
python batch_run.py \
  --csv cagui.csv \
  --start 1 \
  --pod-id YOUR_POD_ID \
  --auth-token YOUR_AUTH_TOKEN \
  --product-id YOUR_PRODUCT_ID \
  --tos-bucket YOUR_TOS_BUCKET \
  --tos-region YOUR_TOS_REGION \
  --tos-endpoint YOUR_TOS_ENDPOINT
```

## 命令行参数

| 参数 | 必需 | 描述 |
|------|------|------|
| `--csv` | ✅ | CAGUI CSV文件路径 |
| `--start` | ❌ | 起始索引（从1开始，默认为1） |
| `--end` | ❌ | 结束索引（包含，不指定则执行到文件末尾） |
| `--config` | ❌ | 批量执行配置文件路径（JSON格式） |
| `--pod-id` | ✅ | 云手机Pod ID |
| `--auth-token` | ✅ | 认证Token |
| `--product-id` | ✅ | 产品ID |
| `--tos-bucket` | ✅ | TOS存储桶名称 |
| `--tos-region` | ✅ | TOS区域 |
| `--tos-endpoint` | ✅ | TOS端点 |
| `--log-level` | ❌ | 日志级别（DEBUG/INFO/WARNING/ERROR，默认INFO） |
| `--output-dir` | ❌ | 结果输出目录（默认batch_results） |

## 配置文件

您可以创建一个JSON配置文件来自定义批量执行的行为。参考 `batch_config_example.json`：

```json
{
  "max_retries": 3,
  "timeout_per_instruction": 300,
  "delay_between_instructions": 2.0,
  "reset_to_home": true,
  "reset_timeout": 10,
  "log_level": "INFO",
  "save_screenshots": true,
  "result_output_path": "batch_results",
  "csv_encoding": "utf-8",
  "continue_on_error": true,
  "save_error_screenshots": true
}
```

### 配置参数说明

- `max_retries`: 单个instruction最大重试次数
- `timeout_per_instruction`: 单个instruction超时时间（秒）
- `delay_between_instructions`: instruction之间的延迟（秒）
- `reset_to_home`: 每个instruction前是否返回主页面
- `reset_timeout`: 状态重置超时时间（秒）
- `log_level`: 日志级别
- `save_screenshots`: 是否保存截图
- `result_output_path`: 结果输出路径
- `csv_encoding`: CSV文件编码
- `continue_on_error`: 单个instruction失败时是否继续执行
- `save_error_screenshots`: 是否保存错误时的截图

## 输出结果

执行完成后，会在输出目录中生成以下文件：

1. **详细结果（JSON）**: `batch_results_YYYYMMDD_HHMMSS.json`
   - 包含每个instruction的详细执行信息
   - 执行状态、耗时、错误信息等

2. **汇总结果（CSV）**: `batch_summary_YYYYMMDD_HHMMSS.csv`
   - 表格格式的执行汇总
   - 便于在Excel等工具中查看和分析

3. **中间结果（JSON）**: `batch_intermediate_YYYYMMDD_HHMMSS.json`
   - 执行过程中的中间结果
   - 用于故障恢复和进度跟踪

## 执行流程

1. **解析CSV文件**: 读取指定范围的instructions
2. **逐个执行**: 
   - 生成独立的thread_id确保消息隔离
   - 返回主页面重置状态
   - 执行instruction
   - 记录执行结果
3. **错误处理**: 支持重试机制，单个失败不影响后续执行
4. **结果保存**: 生成详细的执行报告

## 注意事项

1. **消息隔离**: 每个instruction使用独立的thread_id，确保消息不会相互干扰
2. **状态重置**: 每个instruction执行前会自动返回主页面，保证初始状态一致
3. **错误处理**: 单个instruction失败不会中断整个批量执行过程
4. **资源管理**: 执行完成后会自动清理相关资源

## 故障排除

### 常见问题

1. **CSV文件格式错误**
   - 确保CSV文件包含必需的列：id, instruction, action_trace, expected_result
   - 检查action_trace字段是否为有效的JSON格式

2. **连接问题**
   - 检查云手机Pod ID和认证信息是否正确
   - 确保网络连接正常

3. **执行超时**
   - 调整 `timeout_per_instruction` 配置
   - 检查instruction的复杂度

4. **内存不足**
   - 减少并发执行的instruction数量
   - 增加系统内存

### 日志分析

设置 `--log-level DEBUG` 可以获得更详细的执行日志，有助于问题诊断。

## 编程接口

您也可以在Python代码中直接使用批量执行功能：

```python
from mobile_agent.agent.mobile_use_agent import MobileUseAgent
from mobile_agent.agent.batch.executor import BatchExecutor
from mobile_agent.config.batch_config import get_default_batch_config

# 创建并初始化MobileUseAgent
mobile_agent = MobileUseAgent()
await mobile_agent.initialize(
    pod_id="your_pod_id",
    auth_token="your_auth_token",
    product_id="your_product_id",
    tos_bucket="your_tos_bucket",
    tos_region="your_tos_region",
    tos_endpoint="your_tos_endpoint"
)

# 创建批量执行器
config = get_default_batch_config()
executor = BatchExecutor(mobile_agent, config)

# 执行批量任务
summary = await executor.execute_batch(
    csv_path="cagui.csv",
    start_index=1,
    end_index=10
)

print(f"成功率: {summary.success_rate:.2%}")
```
