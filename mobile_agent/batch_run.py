#!/usr/bin/env python3
"""
批量执行CAGUI instructions的命令行工具

使用示例:
    python batch_run.py --csv cagui.csv --start 1 --end 10
    python batch_run.py --csv cagui.csv --start 5 --end 20 --config batch_config.json
"""

import argparse
import asyncio
import json
import logging
import sys
from pathlib import Path
from typing import Optional

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from mobile_agent.agent.mobile_use_agent import MobileUseAgent
from mobile_agent.agent.batch.executor import BatchExecutor
from mobile_agent.config.batch_config import BatchExecutionConfig, get_default_batch_config

logger = logging.getLogger(__name__)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="批量执行CAGUI instructions",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s --csv cagui.csv --start 1 --end 10
  %(prog)s --csv cagui.csv --start 5 --end 20 --config custom_config.json
  %(prog)s --csv cagui.csv --start 1  # 从第1条执行到文件末尾
        """
    )
    
    parser.add_argument(
        '--csv', 
        required=True,
        help='CAGUI CSV文件路径'
    )
    
    parser.add_argument(
        '--start',
        type=int,
        default=1,
        help='起始索引（从1开始，默认为1）'
    )
    
    parser.add_argument(
        '--end',
        type=int,
        help='结束索引（包含，不指定则执行到文件末尾）'
    )
    
    parser.add_argument(
        '--config',
        help='批量执行配置文件路径（JSON格式）'
    )
    
    parser.add_argument(
        '--pod-id',
        required=True,
        help='云手机Pod ID'
    )
    
    parser.add_argument(
        '--auth-token',
        required=True,
        help='认证Token'
    )
    
    parser.add_argument(
        '--product-id',
        required=True,
        help='产品ID'
    )
    
    parser.add_argument(
        '--tos-bucket',
        required=True,
        help='TOS存储桶名称'
    )
    
    parser.add_argument(
        '--tos-region',
        required=True,
        help='TOS区域'
    )
    
    parser.add_argument(
        '--tos-endpoint',
        required=True,
        help='TOS端点'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别（默认为INFO）'
    )
    
    parser.add_argument(
        '--output-dir',
        default='batch_results',
        help='结果输出目录（默认为batch_results）'
    )
    
    return parser.parse_args()


def load_config(config_path: Optional[str]) -> BatchExecutionConfig:
    """加载批量执行配置"""
    if not config_path:
        return get_default_batch_config()
    
    try:
        config_file = Path(config_path)
        if not config_file.exists():
            logger.warning(f"配置文件不存在: {config_path}，使用默认配置")
            return get_default_batch_config()
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        return BatchExecutionConfig(**config_data)
        
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}，使用默认配置")
        return get_default_batch_config()


async def main():
    """主函数"""
    args = parse_arguments()
    
    # 设置日志
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger.info("开始批量执行CAGUI instructions")
    logger.info(f"CSV文件: {args.csv}")
    logger.info(f"执行范围: {args.start} - {args.end or '末尾'}")
    
    try:
        # 验证CSV文件
        csv_path = Path(args.csv)
        if not csv_path.exists():
            logger.error(f"CSV文件不存在: {args.csv}")
            return 1
        
        # 加载配置
        config = load_config(args.config)
        config.log_level = args.log_level
        config.result_output_path = args.output_dir
        
        # 创建并初始化MobileUseAgent
        mobile_agent = MobileUseAgent()
        await mobile_agent.initialize(
            pod_id=args.pod_id,
            auth_token=args.auth_token,
            product_id=args.product_id,
            tos_bucket=args.tos_bucket,
            tos_region=args.tos_region,
            tos_endpoint=args.tos_endpoint
        )
        
        # 创建批量执行器
        executor = BatchExecutor(mobile_agent, config)
        
        # 执行批量任务
        summary = await executor.execute_batch(
            csv_path=str(csv_path),
            start_index=args.start,
            end_index=args.end
        )
        
        # 输出执行总结
        logger.info("=" * 50)
        logger.info("批量执行完成")
        logger.info(f"总计instructions: {summary.total_instructions}")
        logger.info(f"成功: {summary.success_count}")
        logger.info(f"失败: {summary.failed_count}")
        logger.info(f"跳过: {summary.skipped_count}")
        logger.info(f"成功率: {summary.success_rate:.2%}")
        logger.info(f"总耗时: {summary.total_duration_seconds:.2f}秒")
        logger.info("=" * 50)
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("用户中断执行")
        return 1
    except Exception as e:
        logger.error(f"批量执行失败: {e}")
        return 1
    finally:
        # 清理资源
        try:
            if 'mobile_agent' in locals():
                await mobile_agent.aclose()
        except Exception as e:
            logger.error(f"清理资源时出错: {e}")


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
