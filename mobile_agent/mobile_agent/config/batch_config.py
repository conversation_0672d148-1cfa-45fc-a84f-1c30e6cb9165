from pydantic import BaseModel
from typing import Optional


class BatchExecutionConfig(BaseModel):
    """批量执行配置"""
    
    # 执行控制
    max_retries: int = 3  # 单个instruction最大重试次数
    timeout_per_instruction: int = 300  # 单个instruction超时时间（秒）
    delay_between_instructions: float = 2.0  # instruction之间的延迟（秒）
    
    # 状态重置
    reset_to_home: bool = True  # 每个instruction前是否返回主页面
    reset_timeout: int = 10  # 状态重置超时时间（秒）
    
    # 日志和结果
    log_level: str = "INFO"  # 日志级别
    save_screenshots: bool = True  # 是否保存截图
    result_output_path: str = "batch_results"  # 结果输出路径
    
    # CSV文件配置
    csv_encoding: str = "utf-8"  # CSV文件编码
    
    # 错误处理
    continue_on_error: bool = True  # 单个instruction失败时是否继续执行
    save_error_screenshots: bool = True  # 是否保存错误时的截图


def get_default_batch_config() -> BatchExecutionConfig:
    """获取默认批量执行配置"""
    return BatchExecutionConfig()
