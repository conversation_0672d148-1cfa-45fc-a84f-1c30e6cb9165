from pydantic import BaseModel
from mobile_agent.config.settings import get_settings

class FornaxConfig(BaseModel):
    enable: bool = False
    ak: str = ""
    sk: str = ""

    @classmethod
    def from_settings(cls):
        """从全局配置加载Fornax配置"""
        settings = get_settings()
        return cls(
            enable=getattr(settings, "fornax_enable", False),
            ak=getattr(settings, "fornax_ak", ""),
            sk=getattr(settings, "fornax_sk", "")
        )