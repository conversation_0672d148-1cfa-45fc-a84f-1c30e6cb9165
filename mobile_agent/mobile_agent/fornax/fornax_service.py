from bytedance.fornax.integration.langchain import initialize, FornaxTracer
from mobile_agent.config.fornax_config import FornaxConfig
from mobile_agent.agent.infra.logger import AgentLogger
import bytedance.context as context
import logid

class FornaxService:
    """Fornax功能服务类"""
    
    def __init__(self, config: FornaxConfig):
        self.config = config
        self.logger = AgentLogger(__name__)
        # self.trace_handler = None
        self._maybe_initialize()  # 统一触发可能的初始化

    def _maybe_initialize(self):
        """根据配置动态决定是否初始化 Fornax"""
        if not self.config.enable:
            self.logger.info("Fornax 功能未启用，跳过初始化")
            return

        if not all([self.config.ak, self.config.sk]):
            self.logger.error("Fornax 配置缺失 AK 或 SK，初始化失败")
            return

        try:
            self.logger.info(f"ak={self.config.ak} ,sk={self.config.sk}")
            initialize(self.config.ak, self.config.sk)
            log_id = context.get('logid', '')
            if log_id == '':
                log_id = logid.generate()
                context.set("logid", log_id)
            # self.trace_handler = FornaxTracer.get_callback_handler()
            self.logger.info("Fornax 服务初始化成功")
        except Exception as e:
            self.logger.error(f"Fornax 初始化失败: {str(e)}")
            raise