import asyncio
import uuid
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any

from mobile_agent.agent.mobile_use_agent import MobileUseAgent
from mobile_agent.agent.batch.models import (
    InstructionData, ExecutionResult, ExecutionStatus, BatchExecutionSummary
)
from mobile_agent.agent.batch.csv_parser import CaguiCSVParser
from mobile_agent.config.batch_config import BatchExecutionConfig

logger = logging.getLogger(__name__)


class BatchExecutor:
    """批量执行器"""
    
    def __init__(self, 
                 mobile_agent: MobileUseAgent,
                 config: BatchExecutionConfig):
        self.mobile_agent = mobile_agent
        self.config = config
        self.current_summary: Optional[BatchExecutionSummary] = None
        
        # 创建结果输出目录
        self.output_dir = Path(config.result_output_path)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self._setup_logging()
    
    def _setup_logging(self):
        """设置日志"""
        log_level = getattr(logging, self.config.log_level.upper(), logging.INFO)
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    async def execute_batch(self, 
                           csv_path: str,
                           start_index: int = 1,
                           end_index: Optional[int] = None) -> BatchExecutionSummary:
        """
        批量执行instructions
        
        Args:
            csv_path: CSV文件路径
            start_index: 起始索引（从1开始）
            end_index: 结束索引（包含，None表示到文件末尾）
            
        Returns:
            BatchExecutionSummary: 执行总结
        """
        logger.info(f"开始批量执行: {csv_path}, 范围: {start_index}-{end_index or '末尾'}")
        
        # 解析CSV文件
        parser = CaguiCSVParser(csv_path, self.config.csv_encoding)
        instructions = parser.parse_instructions(start_index, end_index)
        
        if not instructions:
            logger.warning("没有找到需要执行的instructions")
            return BatchExecutionSummary(
                start_index=start_index,
                end_index=end_index or start_index,
                total_instructions=0,
                start_time=datetime.now()
            )
        
        # 初始化执行总结
        self.current_summary = BatchExecutionSummary(
            start_index=start_index,
            end_index=end_index or (start_index + len(instructions) - 1),
            total_instructions=len(instructions),
            start_time=datetime.now()
        )
        
        logger.info(f"准备执行 {len(instructions)} 条instructions")
        
        # 逐个执行instructions
        for i, instruction in enumerate(instructions, 1):
            logger.info(f"执行第 {i}/{len(instructions)} 条instruction (ID: {instruction.id})")
            
            result = await self._execute_single_instruction(instruction)
            self.current_summary.add_result(result)
            
            # 保存中间结果
            await self._save_intermediate_results()
            
            # instruction之间的延迟
            if i < len(instructions):
                await asyncio.sleep(self.config.delay_between_instructions)
        
        # 完成执行
        self.current_summary.end_time = datetime.now()
        self.current_summary.total_duration_seconds = (
            self.current_summary.end_time - self.current_summary.start_time
        ).total_seconds()
        
        # 保存最终结果
        await self._save_final_results()
        
        logger.info(f"批量执行完成: 成功 {self.current_summary.success_count}/"
                   f"{self.current_summary.total_instructions}, "
                   f"成功率: {self.current_summary.success_rate:.2%}")
        
        return self.current_summary
    
    async def _execute_single_instruction(self, instruction: InstructionData) -> ExecutionResult:
        """执行单个instruction"""
        # 生成唯一的thread_id和task_id
        thread_id = f"batch_{instruction.id}_{uuid.uuid4().hex[:8]}"
        task_id = str(uuid.uuid4())
        
        result = ExecutionResult(
            instruction_id=instruction.id,
            instruction_text=instruction.instruction,
            status=ExecutionStatus.RUNNING,
            start_time=datetime.now(),
            thread_id=thread_id,
            task_id=task_id,
            expected_result=instruction.expected_result
        )
        
        try:
            # 状态重置：返回主页面
            if self.config.reset_to_home:
                await self._reset_to_home()
            
            # 执行instruction
            await self._run_instruction_with_retry(instruction, result)
            
        except Exception as e:
            logger.error(f"执行instruction {instruction.id} 时出错: {e}")
            result.status = ExecutionStatus.FAILED
            result.error_message = str(e)
        
        # 完成执行
        result.end_time = datetime.now()
        result.duration_seconds = (result.end_time - result.start_time).total_seconds()
        
        return result

    async def _reset_to_home(self):
        """重置到主页面"""
        try:
            logger.debug("正在重置到主页面...")

            # 使用MCP的home工具返回主页面
            # 初始化mobile_agent的上下文（如果需要）
            # 这里可能需要根据实际的MobileUseAgent实现来调整

            # 调用home工具
            home_tool_call = {
                "name": "mobile_use:home",
                "arguments": {}
            }

            # 通过tools执行home命令
            if hasattr(self.mobile_agent, 'tools') and self.mobile_agent.tools:
                await self.mobile_agent.tools.exec(home_tool_call)

            # 等待重置完成
            await asyncio.sleep(1.0)
            logger.debug("重置到主页面完成")

        except Exception as e:
            logger.warning(f"重置到主页面失败: {e}")
            # 重置失败不应该阻止instruction执行

    async def _run_instruction_with_retry(self, instruction: InstructionData, result: ExecutionResult):
        """带重试的instruction执行"""
        last_error = None

        for attempt in range(self.config.max_retries + 1):
            try:
                if attempt > 0:
                    logger.info(f"重试执行instruction {instruction.id}, 第 {attempt} 次")
                    result.retry_count = attempt

                    # 重试前重新重置状态
                    if self.config.reset_to_home:
                        await self._reset_to_home()

                # 执行instruction
                await self._run_single_instruction(instruction, result)

                # 如果执行成功，跳出重试循环
                if result.status == ExecutionStatus.SUCCESS:
                    break

            except asyncio.TimeoutError:
                last_error = f"执行超时 (第{attempt + 1}次尝试)"
                logger.warning(f"Instruction {instruction.id} {last_error}")
            except Exception as e:
                last_error = f"执行异常: {str(e)} (第{attempt + 1}次尝试)"
                logger.warning(f"Instruction {instruction.id} {last_error}")

        # 如果所有重试都失败了
        if result.status != ExecutionStatus.SUCCESS:
            result.status = ExecutionStatus.FAILED
            result.error_message = last_error or "执行失败"

    async def _run_single_instruction(self, instruction: InstructionData, result: ExecutionResult):
        """执行单个instruction（不含重试逻辑）"""
        try:
            # 创建一个事件用于SSE连接（如果需要）
            sse_connection = asyncio.Event()

            # 设置超时
            timeout_task = asyncio.create_task(
                asyncio.sleep(self.config.timeout_per_instruction)
            )

            # 执行instruction
            execution_task = asyncio.create_task(
                self._execute_instruction_core(instruction, result, sse_connection)
            )

            # 等待执行完成或超时
            done, pending = await asyncio.wait(
                [execution_task, timeout_task],
                return_when=asyncio.FIRST_COMPLETED
            )

            # 取消未完成的任务
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

            # 检查结果
            if timeout_task in done:
                raise asyncio.TimeoutError("执行超时")

            # 获取执行结果
            if execution_task in done:
                await execution_task  # 确保异常被抛出

        except Exception as e:
            logger.error(f"执行instruction {instruction.id} 时出错: {e}")
            raise

    async def _execute_instruction_core(self, instruction: InstructionData,
                                       result: ExecutionResult, sse_connection: asyncio.Event):
        """执行instruction的核心逻辑"""
        try:
            # 解析action_trace
            actions = json.loads(instruction.action_trace)
            logger.debug(f"解析到 {len(actions)} 个动作")

            # 模拟手机屏幕尺寸（可以从配置中获取）
            phone_width = 1080
            phone_height = 2400

            # 使用MobileUseAgent执行instruction
            execution_generator = self.mobile_agent.run(
                query=instruction.instruction,
                is_stream=False,  # 批量执行不使用流式输出
                session_id=result.thread_id,
                thread_id=result.thread_id,
                sse_connection=sse_connection,
                phone_width=phone_width,
                phone_height=phone_height
            )

            # 收集执行结果
            execution_chunks = []
            async for chunk in execution_generator:
                execution_chunks.append(chunk)
                logger.debug(f"收到执行块: {type(chunk)}")

            # 分析执行结果
            await self._analyze_execution_result(instruction, result, execution_chunks)

        except json.JSONDecodeError as e:
            logger.error(f"解析action_trace失败: {e}")
            result.status = ExecutionStatus.FAILED
            result.error_message = f"action_trace格式错误: {str(e)}"
        except Exception as e:
            logger.error(f"执行instruction核心逻辑失败: {e}")
            result.status = ExecutionStatus.FAILED
            result.error_message = str(e)

    async def _analyze_execution_result(self, instruction: InstructionData,
                                       result: ExecutionResult, execution_chunks: List[Any]):
        """分析执行结果"""
        try:
            # 检查是否有finished工具调用
            has_finished = False
            actual_result = ""

            for chunk in execution_chunks:
                if isinstance(chunk, dict):
                    # 检查是否包含finished工具调用
                    if 'messages' in chunk:
                        for message in chunk.get('messages', []):
                            if hasattr(message, 'tool_calls'):
                                for tool_call in message.tool_calls:
                                    if tool_call.get('name') == 'finished':
                                        has_finished = True
                                        actual_result = tool_call.get('arguments', {}).get('content', '')
                                        break

            # 设置执行状态
            if has_finished:
                result.status = ExecutionStatus.SUCCESS
                result.actual_result = actual_result

                # 简单的结果匹配检查
                if instruction.expected_result and actual_result:
                    result.success_match = instruction.expected_result.lower() in actual_result.lower()
                else:
                    result.success_match = None
            else:
                result.status = ExecutionStatus.FAILED
                result.error_message = "未检测到finished工具调用"

            logger.info(f"Instruction {instruction.id} 执行状态: {result.status}")

        except Exception as e:
            logger.error(f"分析执行结果时出错: {e}")
            result.status = ExecutionStatus.FAILED
            result.error_message = f"结果分析失败: {str(e)}"

    async def _save_intermediate_results(self):
        """保存中间结果"""
        if not self.current_summary:
            return

        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"batch_intermediate_{timestamp}.json"
            filepath = self.output_dir / filename

            # 保存为JSON格式
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.current_summary.model_dump(), f, ensure_ascii=False, indent=2, default=str)

            logger.debug(f"中间结果已保存: {filepath}")

        except Exception as e:
            logger.error(f"保存中间结果失败: {e}")

    async def _save_final_results(self):
        """保存最终结果"""
        if not self.current_summary:
            return

        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存JSON格式的详细结果
            json_filename = f"batch_results_{timestamp}.json"
            json_filepath = self.output_dir / json_filename

            with open(json_filepath, 'w', encoding='utf-8') as f:
                json.dump(self.current_summary.model_dump(), f, ensure_ascii=False, indent=2, default=str)

            # 保存CSV格式的汇总结果
            csv_filename = f"batch_summary_{timestamp}.csv"
            csv_filepath = self.output_dir / csv_filename

            await self._save_csv_summary(csv_filepath)

            logger.info(f"最终结果已保存: {json_filepath}, {csv_filepath}")

        except Exception as e:
            logger.error(f"保存最终结果失败: {e}")

    async def _save_csv_summary(self, filepath: Path):
        """保存CSV格式的汇总结果"""
        try:
            import csv

            with open(filepath, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)

                # 写入标题行
                writer.writerow([
                    'instruction_id', 'instruction_text', 'status', 'duration_seconds',
                    'retry_count', 'expected_result', 'actual_result', 'success_match',
                    'error_message', 'start_time', 'end_time'
                ])

                # 写入数据行
                for result in self.current_summary.results:
                    writer.writerow([
                        result.instruction_id,
                        result.instruction_text,
                        result.status.value,
                        result.duration_seconds,
                        result.retry_count,
                        result.expected_result,
                        result.actual_result or '',
                        result.success_match,
                        result.error_message or '',
                        result.start_time.isoformat() if result.start_time else '',
                        result.end_time.isoformat() if result.end_time else ''
                    ])

        except Exception as e:
            logger.error(f"保存CSV汇总失败: {e}")
            raise
