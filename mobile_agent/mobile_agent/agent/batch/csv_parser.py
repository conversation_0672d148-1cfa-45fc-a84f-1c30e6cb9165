import csv
import json
from typing import List, Optional
from pathlib import Path
import logging

from mobile_agent.agent.batch.models import InstructionData

logger = logging.getLogger(__name__)


class CaguiCSVParser:
    """CAGUI CSV文件解析器"""
    
    def __init__(self, csv_path: str, encoding: str = "utf-8"):
        self.csv_path = Path(csv_path)
        self.encoding = encoding
        
        if not self.csv_path.exists():
            raise FileNotFoundError(f"CSV文件不存在: {csv_path}")
    
    def parse_instructions(self, start_index: Optional[int] = None, 
                          end_index: Optional[int] = None) -> List[InstructionData]:
        """
        解析CSV文件中的instructions
        
        Args:
            start_index: 起始索引（从1开始，包含）
            end_index: 结束索引（包含）
            
        Returns:
            List[InstructionData]: 解析后的instruction列表
        """
        instructions = []
        
        try:
            with open(self.csv_path, 'r', encoding=self.encoding) as file:
                # 检测CSV分隔符
                sample = file.read(1024)
                file.seek(0)
                sniffer = csv.Sniffer()
                delimiter = sniffer.sniff(sample).delimiter
                
                reader = csv.DictReader(file, delimiter=delimiter)
                
                for row_num, row in enumerate(reader, start=1):
                    # 应用索引过滤
                    if start_index is not None and row_num < start_index:
                        continue
                    if end_index is not None and row_num > end_index:
                        break
                    
                    try:
                        instruction_data = self._parse_row(row, row_num)
                        if instruction_data:
                            instructions.append(instruction_data)
                    except Exception as e:
                        logger.error(f"解析第{row_num}行时出错: {e}")
                        continue
                        
        except Exception as e:
            logger.error(f"读取CSV文件时出错: {e}")
            raise
        
        logger.info(f"成功解析 {len(instructions)} 条instructions")
        return instructions
    
    def _parse_row(self, row: dict, row_num: int) -> Optional[InstructionData]:
        """解析单行数据"""
        try:
            # 获取基本字段
            instruction_id = int(row.get('id', row_num))
            instruction = row.get('instruction', '').strip()
            action_trace = row.get('action_trace', '').strip()
            expected_result = row.get('expected_result', '').strip()
            
            # 验证必需字段
            if not instruction or not action_trace:
                logger.warning(f"第{row_num}行缺少必需字段，跳过")
                return None
            
            # 解析可选字段
            human_steps = self._safe_int_parse(row.get('human_steps', '0'))
            episode_id = row.get('episode_id', '').strip()
            final_image_path = row.get('final_image_path', '').strip() or None
            difficulty_level = row.get('步骤分级', '').strip() or None
            app_category = row.get('应用分类', '').strip() or None
            
            # 验证action_trace是否为有效JSON
            try:
                json.loads(action_trace)
            except json.JSONDecodeError:
                logger.warning(f"第{row_num}行action_trace不是有效JSON，跳过")
                return None
            
            return InstructionData(
                id=instruction_id,
                instruction=instruction,
                action_trace=action_trace,
                expected_result=expected_result,
                human_steps=human_steps,
                episode_id=episode_id,
                final_image_path=final_image_path,
                difficulty_level=difficulty_level,
                app_category=app_category
            )
            
        except Exception as e:
            logger.error(f"解析第{row_num}行数据时出错: {e}")
            return None
    
    def _safe_int_parse(self, value: str, default: int = 0) -> int:
        """安全的整数解析"""
        try:
            return int(value) if value.strip() else default
        except (ValueError, AttributeError):
            return default
    
    def get_total_count(self) -> int:
        """获取CSV文件中的总行数（不包括标题行）"""
        try:
            with open(self.csv_path, 'r', encoding=self.encoding) as file:
                reader = csv.reader(file)
                next(reader)  # 跳过标题行
                return sum(1 for _ in reader)
        except Exception as e:
            logger.error(f"获取CSV总行数时出错: {e}")
            return 0
