from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class ExecutionStatus(str, Enum):
    """执行状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"


class InstructionData(BaseModel):
    """单个instruction数据"""
    id: int
    instruction: str
    action_trace: str
    expected_result: str
    human_steps: int
    episode_id: str
    final_image_path: Optional[str] = None
    difficulty_level: Optional[str] = None  # 步骤分级
    app_category: Optional[str] = None  # 应用分类


class ExecutionResult(BaseModel):
    """单个instruction执行结果"""
    instruction_id: int
    instruction_text: str
    status: ExecutionStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    thread_id: str
    task_id: str
    
    # 执行详情
    error_message: Optional[str] = None
    retry_count: int = 0
    screenshot_paths: List[str] = []
    
    # 结果分析
    expected_result: str
    actual_result: Optional[str] = None
    success_match: Optional[bool] = None  # 是否匹配期望结果


class BatchExecutionSummary(BaseModel):
    """批量执行总结"""
    start_index: int
    end_index: int
    total_instructions: int
    
    # 执行统计
    success_count: int = 0
    failed_count: int = 0
    skipped_count: int = 0
    
    # 时间统计
    start_time: datetime
    end_time: Optional[datetime] = None
    total_duration_seconds: Optional[float] = None
    
    # 详细结果
    results: List[ExecutionResult] = []
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_instructions == 0:
            return 0.0
        return self.success_count / self.total_instructions
    
    def add_result(self, result: ExecutionResult):
        """添加执行结果"""
        self.results.append(result)
        
        if result.status == ExecutionStatus.SUCCESS:
            self.success_count += 1
        elif result.status == ExecutionStatus.FAILED:
            self.failed_count += 1
        elif result.status == ExecutionStatus.SKIPPED:
            self.skipped_count += 1
