from typing import Literal, Optional
from pydantic import BaseModel


class MessageMeta(BaseModel):
    finish_reason: Optional[str] = None
    model: Optional[str] = None
    prompt_tokens: Optional[int] = None
    total_tokens: Optional[int] = None


class SSEContentMessageData(BaseModel):
    id: str
    task_id: str
    role: str
    content: str
    response_meta: Optional[MessageMeta] = None


class SSEReasoningMessageData(SSEContentMessageData):
    type: Literal["reasoning"] = "reasoning"
    role: Literal["assistant"] = "assistant"


class SSEThinkMessageData(SSEContentMessageData):
    type: Literal["think"] = "think"
    role: Literal["assistant"] = "assistant"


class UserInterruptMessageData(SSEContentMessageData):
    type: Literal["user_interrupt"] = "user_interrupt"
    interrupt_type: Literal["text"]


class SummaryMessageData(SSEContentMessageData):
    type: Literal["summary"] = "summary"


class SSEToolCallMessageData(BaseModel):
    id: str
    task_id: str
    tool_id: str
    type: Literal["tool"] = "tool"
    status: Literal["start", "stop", "success"]
    tool_type: Literal["tool_input", "tool_output"]
    tool_name: str
    tool_input: Optional[str] = None
    tool_output: Optional[str] = None
