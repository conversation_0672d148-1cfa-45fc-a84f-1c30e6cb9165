import logging
from mobile_agent.agent.infra.message_web import SSEThinkMessageData
from mobile_agent.agent.tools.tool.abc import SpecialTool

logger = logging.getLogger(__name__)


class ErrorTool(SpecialTool):
    def __init__(self):
        super().__init__(
            name="error_action",
            description="If the model output is not in the correct format, call this action. You must summary the task result in content.",
            parameters={},
        )

    async def handler(
        self,
        args: dict,
    ):
        content = args.get("content")
        logger.error(f"模型输出解析失败，正在尝试重新生成: {content}")
        return "模型输出解析失败，正在尝试重新生成"

    def special_message(self, content: str, args: dict):
        return SSEThinkMessageData(
            id=args.get("chunk_id"),
            task_id=args.get("task_id"),
            role="assistant",
            type="think",
            content=content,
        )

    def special_memory(self, content: str = ""):
        return """模型输出解析失败，请尝试重新按照正确的格式生成
```
Summary: ...
Action: ...
```
注意：
1. 不要输出 ``` ，直接输出 Summary: ...\nAction: ...
2. 遵循系统提示词的 ActionSpace 的Action做输出
"""
