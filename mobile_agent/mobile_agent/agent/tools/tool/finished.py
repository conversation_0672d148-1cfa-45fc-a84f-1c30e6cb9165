from mobile_agent.agent.infra.message_web import SummaryMessageData
from mobile_agent.agent.tools.tool.abc import SpecialTool


class FinishedTool(SpecialTool):
    def __init__(self):
        super().__init__(
            name="finished",
            description="If the task is completed, call this action. You must summary the task result in content.",
            parameters={
                "content": {
                    "type": "string",
                    "description": "The content to summary the task result",
                }
            },
        )

    async def handler(self, args: dict):
        return args.get("content")

    def special_message(self, content: str, args: dict):
        return SummaryMessageData(
            id=args.get("chunk_id"),
            task_id=args.get("task_id"),
            role="assistant",
            type="summary",
            content=content,
        )

    def special_memory(self, content: str = ""):
        return f"""上一轮任务已经完成，结果是:{content},
更多的根据用户新的输入完成任务"""
