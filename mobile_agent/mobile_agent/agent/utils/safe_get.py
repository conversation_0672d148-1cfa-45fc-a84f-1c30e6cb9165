def safe_get(obj, path, default=None):
    """
    Args:
        obj: 要访问的对象
        path: 访问路径，如 'args.0.0.value.request_user'
        default: 默认值

    Returns:
        获取到的值或默认值
    """
    try:
        keys = path.split(".")
        result = obj
        for key in keys:
            if key.isdigit():
                # 处理数组索引
                result = result[int(key)]
            elif hasattr(result, key):
                # 处理对象属性
                result = getattr(result, key)
            elif isinstance(result, dict) and key in result:
                # 处理字典键
                result = result[key]
            else:
                return default
        return result
    except (AttributeError, IndexError, KeyError, TypeError, ValueError):
        return default
