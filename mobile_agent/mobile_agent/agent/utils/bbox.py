TRAIN_WIDTH = 1000  # FOR UI_TARS
TRAIN_HEIGHT = 1000  # FOR UI_TARS


def regular_bbox_for_ui_tars(x1, y1, x2, y2, width, height):
    x1_reg = int((x1 * width) / TRAIN_WIDTH)
    y1_reg = int((y1 * height) / TRAIN_HEIGHT)
    x2_reg = int((x2 * width) / TRAIN_WIDTH)
    y2_reg = int((y2 * height) / TRAIN_HEIGHT)
    return [x1_reg, y1_reg, x2_reg, y2_reg]


def bbox_to_point(bbox):
    x1 = bbox[0]
    y1 = bbox[1]
    x2 = bbox[2]
    y2 = bbox[3]
    return [(x1 + x2) / 2, (y1 + y2) / 2]
