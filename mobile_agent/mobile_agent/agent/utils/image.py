import httpx
from PIL import Image
from io import BytesIO


async def get_dimensions_from_url(screenshot_url: str) -> tuple[int, int]:
    """更新截图的尺寸信息"""
    try:
        # 下载图片
        async with httpx.AsyncClient() as client:
            response = await client.get(screenshot_url)
            if response.is_error:
                return (0, 0)
        # 使用 PIL 获取图片尺寸
        image = Image.open(BytesIO(response.content))
        width, height = image.size
        image.close()
        return (width, height)
    except Exception as e:
        raise e
