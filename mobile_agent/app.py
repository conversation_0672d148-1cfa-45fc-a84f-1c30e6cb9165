from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# 导入配置
from mobile_agent.middleware.middleware import (
    ResponseMiddleware,
    AuthMiddleware,
)
from mobile_agent.config.settings import settings
from mobile_agent.routers.base import register_routers


# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    description="HTTP Server for Mobile Agent",
    version=settings.app_version,
)

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加响应格式中间件
app.add_middleware(ResponseMiddleware)
# 添加账户鉴权中间件
app.add_middleware(AuthMiddleware)

# 注册所有路由
register_routers(app)
