#!/usr/bin/env python3
"""
获取云手机认证信息的脚本
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from mobile_agent.service.pod.manager import pod_manager


def get_auth_info(device_id: str = None, product_id: str = None):
    """获取认证信息"""
    
    # 使用默认值或环境变量
    if not device_id:
        device_id = os.getenv('ACEP_DEVICE_ID', 'default_device_id')
    
    if not product_id:
        product_id = os.getenv('ACEP_PRODUCT_ID', os.getenv('ACEP_ACCOUNT_ID', 'default_product_id'))
    
    print(f"正在获取认证信息...")
    print(f"Device ID: {device_id}")
    print(f"Product ID: {product_id}")
    print(f"Account ID: {pod_manager.account_id}")
    
    try:
        # 获取资源信息
        resource = pod_manager.get_resource(
            device_id=device_id,
            product_id=product_id
        )
        
        print("\n✅ 认证信息获取成功!")
        print("=" * 50)
        
        # 提取关键信息
        device_info = resource['device_info']
        auth_info = resource['auth_info']
        
        print("📱 设备信息:")
        print(f"  Product ID: {device_info['product_id']}")
        print(f"  Device ID: {device_info['device_id']}")
        print(f"  Account ID: {device_info['account_id']}")
        print(f"  屏幕尺寸: {device_info['width']}x{device_info['height']}")
        print(f"  剩余时间: {device_info['free_time']}秒")
        
        print("\n🔐 认证信息:")
        print(f"  Authorization: {auth_info['authorization'][:50]}...")
        print(f"  Access Key: {auth_info['ak']}")
        print(f"  Secret Key: {auth_info['sk'][:20]}...")
        print(f"  Session Token: {auth_info['session_token'][:50]}...")
        print(f"  当前时间: {auth_info['current_time']}")
        print(f"  过期时间: {auth_info['expired_time']}")
        
        print("\n📋 批量执行命令:")
        print("python batch_run.py \\")
        print("  --csv cagui.csv \\")
        print("  --start 1 --end 5 \\")
        print(f"  --pod-id \"{device_info['device_id']}\" \\")
        print(f"  --auth-token \"{auth_info['authorization']}\" \\")
        print(f"  --product-id \"{device_info['product_id']}\"")
        
        print("\n💾 环境变量设置:")
        print(f"export ACEP_POD_ID=\"{device_info['device_id']}\"")
        print(f"export ACEP_AUTH_TOKEN=\"{auth_info['authorization']}\"")
        print(f"export ACEP_PRODUCT_ID=\"{device_info['product_id']}\"")
        
        # 保存到文件
        auth_file = Path("auth_info.json")
        with open(auth_file, 'w', encoding='utf-8') as f:
            json.dump(resource, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 认证信息已保存到: {auth_file}")
        
        return resource
        
    except Exception as e:
        print(f"\n❌ 获取认证信息失败: {e}")
        print("\n请检查以下环境变量是否正确设置:")
        print(f"  ACEP_AK: {os.getenv('ACEP_AK', '未设置')}")
        print(f"  ACEP_SK: {os.getenv('ACEP_SK', '未设置')}")
        print(f"  ACEP_ACCOUNT_ID: {os.getenv('ACEP_ACCOUNT_ID', '未设置')}")
        return None


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="获取云手机认证信息")
    parser.add_argument('--device-id', help='设备ID')
    parser.add_argument('--product-id', help='产品ID')
    
    args = parser.parse_args()
    
    result = get_auth_info(args.device_id, args.product_id)
    
    if result:
        print("\n🎉 认证信息获取完成！现在您可以使用上面的命令进行批量执行。")
        return 0
    else:
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
