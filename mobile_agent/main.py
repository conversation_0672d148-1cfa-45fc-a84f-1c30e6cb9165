import uvicorn
import logging
from mobile_agent.config.settings import settings


# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def main():
    # 打印配置信息
    logger.info(f"Starting {settings.app_name} v{settings.app_version}")
    logger.info(f"Environment: {settings.env}")
    logger.info(
        f"Server config: host={settings.server.host}, port={settings.server.port}"
    )

    # 启动服务器
    uvicorn.run("app:app", host=settings.server.host, port=settings.server.port)


if __name__ == "__main__":
    main()
