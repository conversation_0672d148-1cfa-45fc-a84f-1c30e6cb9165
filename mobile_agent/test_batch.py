#!/usr/bin/env python3
"""
测试批量执行功能的简单脚本
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from mobile_agent.agent.batch.csv_parser import CaguiCSVParser
from mobile_agent.config.batch_config import get_default_batch_config


def test_csv_parser():
    """测试CSV解析器"""
    print("测试CSV解析器...")
    
    csv_path = "cagui.csv"
    if not Path(csv_path).exists():
        print(f"CSV文件不存在: {csv_path}")
        return False
    
    try:
        parser = CaguiCSVParser(csv_path)
        
        # 测试获取总数
        total_count = parser.get_total_count()
        print(f"CSV文件总行数: {total_count}")
        
        # 测试解析前5条
        instructions = parser.parse_instructions(start_index=1, end_index=5)
        print(f"成功解析 {len(instructions)} 条instructions")
        
        for i, instruction in enumerate(instructions, 1):
            print(f"  {i}. ID: {instruction.id}, 指令: {instruction.instruction[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"CSV解析测试失败: {e}")
        return False


def test_config():
    """测试配置"""
    print("\n测试配置...")
    
    try:
        config = get_default_batch_config()
        print(f"默认配置加载成功:")
        print(f"  最大重试次数: {config.max_retries}")
        print(f"  单个instruction超时: {config.timeout_per_instruction}秒")
        print(f"  instruction间延迟: {config.delay_between_instructions}秒")
        print(f"  是否重置到主页面: {config.reset_to_home}")
        
        return True
        
    except Exception as e:
        print(f"配置测试失败: {e}")
        return False


def main():
    """主函数"""
    print("开始测试批量执行功能...")
    
    success = True
    
    # 测试CSV解析器
    if not test_csv_parser():
        success = False
    
    # 测试配置
    if not test_config():
        success = False
    
    if success:
        print("\n✅ 所有测试通过！")
        print("\n使用示例:")
        print("python batch_run.py --csv cagui.csv --start 1 --end 5 \\")
        print("  --pod-id YOUR_POD_ID \\")
        print("  --auth-token YOUR_AUTH_TOKEN \\")
        print("  --product-id YOUR_PRODUCT_ID \\")
        print("  --tos-bucket YOUR_TOS_BUCKET \\")
        print("  --tos-region YOUR_TOS_REGION \\")
        print("  --tos-endpoint YOUR_TOS_ENDPOINT")
    else:
        print("\n❌ 部分测试失败")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
