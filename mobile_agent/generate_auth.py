#!/usr/bin/env python3
"""
生成正确的认证token
"""

import os
import sys
import json
import base64
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def generate_auth_token():
    """生成认证token"""
    
    # 从环境变量获取参数
    ak = os.getenv('ACEP_AK', '').strip().strip('"').strip("'")
    sk = os.getenv('ACEP_SK', '').strip().strip('"').strip("'")
    account_id = os.getenv('ACEP_ACCOUNT_ID', '').strip().strip('"').strip("'")
    pod_id = os.getenv('ACEP_POD_ID', '7507161356387801883').strip().strip('"').strip("'")
    product_id = os.getenv('ACEP_PRODUCT_ID', '1925071050769436672').strip().strip('"').strip("'")
    
    print("🔍 检查环境变量:")
    print(f"  ACEP_AK: {ak[:20]}..." if ak else "  ACEP_AK: 未设置")
    print(f"  ACEP_SK: {sk[:20]}..." if sk else "  ACEP_SK: 未设置")
    print(f"  ACEP_ACCOUNT_ID: {account_id}")
    print(f"  ACEP_POD_ID: {pod_id}")
    print(f"  ACEP_PRODUCT_ID: {product_id}")
    
    if not ak or not sk or not account_id:
        print("\n❌ 缺少必要的环境变量，请检查.env文件")
        return None
    
    try:
        # 导入volcengine相关模块
        from volcengine.base.Service import Service
        from pydantic import BaseModel
        
        class Credentials(BaseModel):
            ak: str
            sk: str
        
        class ServiceInfo(BaseModel):
            credentials: Credentials
        
        print("\n🔧 正在生成认证token...")
        
        # 创建volcengine服务
        volc_client = Service(
            service_info=ServiceInfo(
                credentials=Credentials(ak=ak, sk=sk)
            ),
            api_info={},
        )
        
        # 生成STS token
        volc_policy = {
            "Statement": [
                {"Effect": "Allow", "Action": ["ACEP:PodStart"], "Resource": ["*"]}
            ]
        }
        
        volc_user_token = volc_client.sign_sts2(
            policy=volc_policy, 
            expire=1000 * 60 * 30  # 30分钟
        )
        
        # 构建认证信息
        auth_dict = {
            "AccessKeyId": volc_user_token.access_key_id,
            "SecretAccessKey": volc_user_token.secret_access_key,
            "CurrentTime": volc_user_token.current_time,
            "ExpiredTime": volc_user_token.expired_time,
            "SessionToken": volc_user_token.session_token,
        }
        
        # 编码为base64
        auth_bytes = json.dumps(auth_dict).encode("utf-8")
        auth_token = base64.b64encode(auth_bytes).decode("utf-8")
        
        print("✅ 认证token生成成功!")
        print(f"🔑 Auth Token: {auth_token[:50]}...")
        
        # 构建完整的设备和认证信息
        device_info = {
            "product_id": product_id,
            "device_id": pod_id,
            "free_time": 9999999,
            "width": 720,
            "height": 1520,
            "account_id": account_id,
        }
        
        auth_info = {
            "authorization": auth_token,
            "ak": volc_user_token.access_key_id,
            "sk": volc_user_token.secret_access_key,
            "session_token": volc_user_token.session_token,
            "current_time": volc_user_token.current_time,
            "expired_time": volc_user_token.expired_time,
        }
        
        print("\n📋 批量执行命令:")
        print("python batch_run.py \\")
        print("  --csv cagui.csv \\")
        print("  --start 1 --end 2 \\")
        print(f"  --pod-id \"{pod_id}\" \\")
        print(f"  --auth-token \"{auth_token}\" \\")
        print(f"  --product-id \"{product_id}\"")
        
        print("\n💾 更新.env文件:")
        print(f"ACEP_POD_ID={pod_id}")
        print(f"ACEP_AUTH_TOKEN={auth_token}")
        print(f"ACEP_PRODUCT_ID={product_id}")
        
        # 保存到文件
        result = {
            "device_info": device_info,
            "auth_info": auth_info
        }
        
        with open("auth_info.json", 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 认证信息已保存到: auth_info.json")
        
        return {
            "pod_id": pod_id,
            "auth_token": auth_token,
            "product_id": product_id
        }
        
    except Exception as e:
        print(f"\n❌ 生成认证token失败: {e}")
        print("请检查volcengine SDK是否正确安装")
        return None


def update_env_file(pod_id, auth_token, product_id):
    """更新.env文件"""
    try:
        env_file = Path(".env")
        if not env_file.exists():
            print("❌ .env文件不存在")
            return False
        
        # 读取现有内容
        with open(env_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 更新相关行
        updated_lines = []
        updated_keys = set()
        
        for line in lines:
            if line.strip().startswith('ACEP_POD_ID='):
                updated_lines.append(f"ACEP_POD_ID={pod_id}\n")
                updated_keys.add('ACEP_POD_ID')
            elif line.strip().startswith('ACEP_AUTH_TOKEN='):
                updated_lines.append(f"ACEP_AUTH_TOKEN={auth_token}\n")
                updated_keys.add('ACEP_AUTH_TOKEN')
            elif line.strip().startswith('ACEP_PRODUCT_ID='):
                updated_lines.append(f"ACEP_PRODUCT_ID={product_id}\n")
                updated_keys.add('ACEP_PRODUCT_ID')
            else:
                updated_lines.append(line)
        
        # 添加缺失的键
        if 'ACEP_POD_ID' not in updated_keys:
            updated_lines.append(f"ACEP_POD_ID={pod_id}\n")
        if 'ACEP_AUTH_TOKEN' not in updated_keys:
            updated_lines.append(f"ACEP_AUTH_TOKEN={auth_token}\n")
        if 'ACEP_PRODUCT_ID' not in updated_keys:
            updated_lines.append(f"ACEP_PRODUCT_ID={product_id}\n")
        
        # 写回文件
        with open(env_file, 'w', encoding='utf-8') as f:
            f.writelines(updated_lines)
        
        print("✅ .env文件已更新")
        return True
        
    except Exception as e:
        print(f"❌ 更新.env文件失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始生成认证token...")
    
    result = generate_auth_token()
    
    if result:
        print("\n🎯 是否要更新.env文件? (y/n): ", end="")
        try:
            choice = input().strip().lower()
            if choice in ['y', 'yes']:
                update_env_file(
                    result['pod_id'],
                    result['auth_token'],
                    result['product_id']
                )
        except KeyboardInterrupt:
            print("\n操作已取消")
        
        print("\n🎉 认证token生成完成！现在您可以使用上面的命令进行批量执行。")
        return 0
    else:
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
